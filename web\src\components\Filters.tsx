import {
  Button,
  TextField,
  Autocomplete,
  Paper,
  Box,
  Chip,
  Tooltip,
  Fade,
} from "@mui/material";
import {
  Public,
  Analytics,
  Visibility,
  Search as SearchIcon,
} from "@mui/icons-material";
import React, { FormEvent, useContext } from "react";
import { useQueryClient } from "react-query";
import styled from "styled-components";
import { useChannels } from "../hooks/useChannels";
import { store } from "../store";
import { Docs } from "./Docs";
import { Optout } from "./Optout";
import { Settings } from "./Settings";
import { Link } from "react-router-dom";

const FiltersWrapper = styled.div`
  padding: 16px;
  background: transparent;
  box-shadow: none;
`;

export function Filters() {
  const { setCurrents, state } = useContext(store);
  const queryClient = useQueryClient();
  const channels = useChannels();

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (e.target instanceof HTMLFormElement) {
      const data = new FormData(e.target);

      const channel = data.get("channel") as string | null;
      const username = data.get("username") as string | null;

      queryClient.invalidateQueries([
        "log",
        { channel: channel?.toLowerCase(), username: username?.toLowerCase() },
      ]);

      setCurrents(channel, username);
    }
  };

  return (
    <FiltersWrapper>
      <Fade in timeout={800}>
        <Paper
          elevation={8}
          sx={{
            p: 3,
            background: "rgba(26, 26, 26, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: 3,
            maxWidth: 1200,
            margin: "0 auto",
          }}
        >
          <Box component="form" onSubmit={handleSubmit} action="none">
            <Box
              display="flex"
              flexWrap="wrap"
              alignItems="center"
              gap={2}
              justifyContent="center"
            >
              {/* Channel Input */}
              <Autocomplete
                id="autocomplete-channels"
                options={channels.map((channel) => channel.name)}
                sx={{ minWidth: 200, flexGrow: 1, maxWidth: 250 }}
                defaultValue={state.currentChannel}
                getOptionLabel={(channel: string) => channel}
                clearOnBlur={false}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    name="channel"
                    label="Channel or ID"
                    variant="outlined"
                    size="small"
                    autoFocus={state.currentChannel === null}
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <SearchIcon sx={{ mr: 1, color: "primary.main" }} />
                      ),
                    }}
                  />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Chip label={`#${option}`} size="small" sx={{ mr: 1 }} />
                    {option}
                  </Box>
                )}
              />

              {/* Username Input */}
              <TextField
                error={state.error}
                name="username"
                label="Username or ID"
                variant="outlined"
                size="small"
                autoComplete="off"
                defaultValue={state.currentUsername}
                autoFocus={
                  state.currentChannel !== null &&
                  state.currentUsername === null
                }
                sx={{ minWidth: 200, flexGrow: 1, maxWidth: 250 }}
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ mr: 1, color: "primary.main" }} />
                  ),
                }}
              />

              {/* Load Button */}
              <Tooltip title="Load messages for the specified channel and user">
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  type="submit"
                  sx={{
                    minWidth: 100,
                    borderRadius: 2,
                    textTransform: "none",
                    fontWeight: "bold",
                  }}
                >
                  Load
                </Button>
              </Tooltip>

              {/* Navigation Buttons */}
              <Box display="flex" gap={1} flexWrap="wrap">
                <Tooltip title="Search across all channels">
                  <Button
                    component={Link}
                    to="/search"
                    variant="outlined"
                    size="large"
                    startIcon={<Public />}
                    sx={{
                      borderRadius: 2,
                      textTransform: "none",
                    }}
                  >
                    Global Search
                  </Button>
                </Tooltip>

                <Tooltip title="View analytics dashboard">
                  <Button
                    component={Link}
                    to="/analytics"
                    variant="outlined"
                    size="large"
                    startIcon={<Analytics />}
                    sx={{
                      borderRadius: 2,
                      textTransform: "none",
                    }}
                  >
                    Analytics
                  </Button>
                </Tooltip>

                <Tooltip title="Track specific users across channels">
                  <Button
                    component={Link}
                    to="/tracker"
                    variant="outlined"
                    size="large"
                    startIcon={<Visibility />}
                    sx={{
                      borderRadius: 2,
                      textTransform: "none",
                    }}
                  >
                    User Tracker
                  </Button>
                </Tooltip>
              </Box>

              {/* Settings and Options */}
              <Box display="flex" gap={1}>
                <Settings />
                <Docs />
                <Optout />
              </Box>
            </Box>
          </Box>
        </Paper>
      </Fade>
    </FiltersWrapper>
  );
}
