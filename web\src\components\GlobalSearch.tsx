import {
  InputAdornment,
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Switch,
  FormControlLabel,
  Collapse,
  Grid,
  Checkbox,
  Autocomplete,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Card,
  CardContent,
  Alert,
} from "@mui/material";
import {
  Search,
  FilterList,
  Public,
  ExpandMore,
  ExpandLess,
  Clear,
  Tune,
} from "@mui/icons-material";
import React, { useState, useRef, useEffect } from "react";
import styled from "styled-components";
import { useGlobalSearch } from "../hooks/useGlobalSearch";
import { SearchResults } from "./SearchResults";
import { useChannels } from "../hooks/useChannels";
import dayjs, { Dayjs } from "dayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";

const GlobalSearchContainer = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .search-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
  }

  .search-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
  }

  .search {
    width: 400px;
  }

  .info-panel {
    padding: 16px;
    margin-bottom: 16px;
    background: var(--bg-bright);
    border-radius: 8px;
  }
`;

interface AdvancedSearchParams {
  q?: string;
  regex?: boolean;
  caseSensitive?: boolean;
  channels?: string[];
  excludeChannels?: string[];
  users?: string[];
  excludeUsers?: string[];
  messageTypes?: string[];
  badges?: string[];
  from?: Dayjs | null;
  to?: Dayjs | null;
  minLength?: number;
  maxLength?: number;
  firstMessagesOnly?: boolean;
  subscribersOnly?: boolean;
  vipsOnly?: boolean;
  moderatorsOnly?: boolean;
  hasEmotes?: boolean;
  searchDisplayNames?: boolean;
}

export function GlobalSearch() {
  const [searchText, setSearchText] = useState("");
  const [showAdvancedFields, setShowAdvancedFields] = useState(false);
  const [advancedSearchParams, setAdvancedSearchParams] = useState<AdvancedSearchParams>({});
  const [isAdvancedSearchActive, setIsAdvancedSearchActive] = useState(false);
  const [useAdvancedMode, setUseAdvancedMode] = useState(false);
  const search = useRef<HTMLInputElement>(null);
  const channels = useChannels();

  // Check for search parameter in URL on component mount
  useEffect(() => {
    const url = new URL(window.location.href);
    const searchParam = url.searchParams.get("search");
    if (searchParam) {
      setSearchText(searchParam);
      // Clear the search parameter from URL after setting it
      url.searchParams.delete("search");
      window.history.replaceState({}, "justlog", url.toString());
    }
  }, []);

  // Convert AdvancedSearchParams to the format expected by useGlobalSearch
  const convertToGlobalSearchParams = (params: AdvancedSearchParams) => ({
    q: params.q,
    regex: params.regex,
    caseSensitive: params.caseSensitive,
    channels: params.channels,
    excludeChannels: params.excludeChannels,
    users: params.users,
    excludeUsers: params.excludeUsers,
    messageTypes: params.messageTypes,
    badges: params.badges,
    from: params.from ? params.from.toDate() : null,
    to: params.to ? params.to.toDate() : null,
    minLength: params.minLength,
    maxLength: params.maxLength,
    firstMessagesOnly: params.firstMessagesOnly,
    subscribersOnly: params.subscribersOnly,
    vipsOnly: params.vipsOnly,
    moderatorsOnly: params.moderatorsOnly,
    hasEmotes: params.hasEmotes,
    searchDisplayNames: params.searchDisplayNames,
  });

  const globalSearchParams = useAdvancedMode
    ? convertToGlobalSearchParams(advancedSearchParams)
    : { q: searchText };

  const {
    data: searchResults,
    isLoading,
    error,
  } = useGlobalSearch(
    globalSearchParams,
    (useAdvancedMode && (!!advancedSearchParams.q || (advancedSearchParams.users && advancedSearchParams.users.length > 0))) || (!useAdvancedMode && !!searchText)
  );

  const handleClearAdvancedSearch = () => {
    setAdvancedSearchParams({});
    setIsAdvancedSearchActive(false);
    setUseAdvancedMode(false);
  };

  useEffect(() => {
    if (search.current) {
      search.current.focus();
    }
  }, []);

  return (
    <GlobalSearchContainer>
      <div className="search-header">
        <Public color="primary" fontSize="large" />
        <Typography variant="h4" component="h1">
          Global Search
        </Typography>
      </div>

      <Paper className="info-panel" elevation={1}>
        <Typography variant="body1" gutterBottom>
          Search across all logged channels and users. Use the advanced search for more precise filtering.
        </Typography>
        {useAdvancedMode && (
          <Typography variant="body2" color="text.secondary" gutterBottom>
            💡 Tip: You can search for specific users by adding them to "User Filters" without entering a search query. A 30-day date range will be applied automatically.
          </Typography>
        )}
        <FormControlLabel
          control={<Switch checked={useAdvancedMode} onChange={(e) => { setUseAdvancedMode(e.target.checked); if (!e.target.checked) { handleClearAdvancedSearch(); } }} />}
          label="Advanced Search Mode"
        />
      </Paper>

      <Box className="search-container">
        <TextField
          className="search"
          label={useAdvancedMode ? "Advanced Search Query" : "Search across all channels"}
          inputRef={search}
          value={useAdvancedMode ? advancedSearchParams.q || "" : searchText}
          onChange={(e) => {
            if (useAdvancedMode) {
              setAdvancedSearchParams((prev) => ({ ...prev, q: e.target.value }));
            } else {
              setSearchText(e.target.value);
            }
          }}
          size="small"
          InputProps={{
            startAdornment: <InputAdornment position="start"><Search /></InputAdornment>,
          }}
          placeholder={useAdvancedMode ? "Enter search terms..." : "Type to search all channels..."}
        />
        {useAdvancedMode && (
          <IconButton onClick={() => setShowAdvancedFields(!showAdvancedFields)} title="Toggle Advanced Search Options" size="small" color={showAdvancedFields ? "primary" : "default"}>
            <Tune />
          </IconButton>
        )}
      </Box>

      {/* Inline Advanced Search Fields */}
      {useAdvancedMode && (
        <Collapse in={showAdvancedFields}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Card sx={{ mt: 2, mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom display="flex" alignItems="center" gap={1}>
                  <Tune />
                  Advanced Search Options
                  <IconButton size="small" onClick={() => setShowAdvancedFields(false)} sx={{ ml: "auto" }}>
                    <ExpandLess />
                  </IconButton>
                </Typography>

                <Grid container spacing={3}>
                  {/* Search Options */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>Search Options</Typography>
                    <Box display="flex" flexWrap="wrap" gap={2}>
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.regex || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, regex: e.target.checked }))} />} label="Regular Expression" />
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.caseSensitive || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, caseSensitive: e.target.checked }))} />} label="Case Sensitive" />
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.searchDisplayNames || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, searchDisplayNames: e.target.checked }))} />} label="Search Display Names" />
                    </Box>
                  </Grid>

                  {/* Channel Filters */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>Channel Filters</Typography>
                    <Autocomplete
                      multiple
                      options={channels.map((channel) => channel.name)}
                      value={advancedSearchParams.channels || []}
                      onChange={(_, newValue) => setAdvancedSearchParams((prev) => ({ ...prev, channels: newValue }))}
                      renderInput={(params) => <TextField {...params} label="Include Channels" placeholder="Select channels..." size="small" />}
                      renderTags={(value, getTagProps) => value.map((option, index) => <Chip variant="outlined" label={`#${option}`} size="small" {...getTagProps({ index })} key={option} />)}
                    />
                  </Grid>

                  {/* User Filters */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>User Filters</Typography>
                    <Autocomplete
                      multiple
                      freeSolo
                      options={[]}
                      value={advancedSearchParams.users || []}
                      onChange={(_, newValue) => setAdvancedSearchParams((prev) => ({ ...prev, users: newValue }))}
                      renderInput={(params) => <TextField {...params} label="Include Users" placeholder="Type usernames..." size="small" />}
                      renderTags={(value, getTagProps) => value.map((option, index) => <Chip variant="outlined" label={option} size="small" {...getTagProps({ index })} key={option} />)}
                    />
                  </Grid>

                  {/* Date Range */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>Date Range</Typography>
                    <Box display="flex" gap={2}>
                      <DateTimePicker label="From" value={advancedSearchParams.from} onChange={(newValue) => setAdvancedSearchParams((prev) => ({ ...prev, from: newValue }))} slotProps={{ textField: { size: "small" } }} />
                      <DateTimePicker label="To" value={advancedSearchParams.to} onChange={(newValue) => setAdvancedSearchParams((prev) => ({ ...prev, to: newValue }))} slotProps={{ textField: { size: "small" } }} />
                    </Box>
                  </Grid>

                  {/* Message Length */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>Message Length</Typography>
                    <Box display="flex" gap={2}>
                      <TextField label="Min Length" type="number" size="small" value={advancedSearchParams.minLength || ""} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, minLength: e.target.value ? parseInt(e.target.value) : undefined }))} />
                      <TextField label="Max Length" type="number" size="small" value={advancedSearchParams.maxLength || ""} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, maxLength: e.target.value ? parseInt(e.target.value) : undefined }))} />
                    </Box>
                  </Grid>

                  {/* User Type Filters */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>User Type Filters</Typography>
                    <Box display="flex" flexWrap="wrap" gap={2}>
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.subscribersOnly || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, subscribersOnly: e.target.checked }))} />} label="Subscribers Only" />
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.vipsOnly || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, vipsOnly: e.target.checked }))} />} label="VIPs Only" />
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.moderatorsOnly || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, moderatorsOnly: e.target.checked }))} />} label="Moderators Only" />
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.firstMessagesOnly || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, firstMessagesOnly: e.target.checked }))} />} label="First Messages Only" />
                      <FormControlLabel control={<Checkbox checked={advancedSearchParams.hasEmotes || false} onChange={(e) => setAdvancedSearchParams((prev) => ({ ...prev, hasEmotes: e.target.checked }))} />} label="Has Emotes" />
                    </Box>
                  </Grid>

                  {/* Clear Advanced Search */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2" color="text.secondary">
                        {Object.keys(advancedSearchParams).length > 0 ? `${Object.keys(advancedSearchParams).length} advanced filters active` : "No advanced filters active"}
                      </Typography>
                      <IconButton onClick={() => { setAdvancedSearchParams({}); setIsAdvancedSearchActive(false); }} title="Clear all advanced search parameters" size="small">
                        <Clear />
                      </IconButton>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </LocalizationProvider>
        </Collapse>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error.message}
        </Alert>
      )}

      {(searchResults || isLoading || error) && (
        <SearchResults
          results={searchResults}
          isLoading={isLoading}
          error={error}
          searchQuery={useAdvancedMode ? advancedSearchParams.q : searchText}
          onClearSearch={useAdvancedMode ? handleClearAdvancedSearch : () => setSearchText("")}
          isGlobalSearch={true}
        />
      )}
    </GlobalSearchContainer>
  );
}
