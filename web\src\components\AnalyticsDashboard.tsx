import React, { useContext } from "react";
import {
  Box,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  Paper,
} from "@mui/material";
import {
  ActivityHeatmapChart,
  MessageTypeDistributionChart,
  ActivityTimelineChart,
  TopChannelsChart,
  InfluenceNetworkChart,
  MessageLengthDistributionChart,
  UserActivityPatternChart,
  ChannelComparisonChart,
} from "./AnalyticsCharts";
import { useQuery } from "react-query";
import { store } from "../store";
import dayjs from "dayjs";

export function AnalyticsDashboard() {
  const { state } = useContext(store);

  const { data, isLoading, error } = useQuery(
    ["global-analytics"],
    async () => {
      const from = dayjs().subtract(30, "day").toISOString();
      const to = dayjs().toISOString();

      const params = new URLSearchParams({
        from,
        to,
      });

      const response = await fetch(`${state.apiBaseUrl}/analytics?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch analytics data");
      }

      return response.json();
    }
  );

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading analytics data...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        Failed to load analytics data: {(error as Error).message}
      </Alert>
    );
  }

  if (!data) {
    return (
      <Paper sx={{ p: 4, textAlign: "center" }}>
        <Typography variant="body1" color="text.secondary">
          No analytics data available.
        </Typography>
      </Paper>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Analytics Dashboard
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <ActivityHeatmapChart data={data.activityHeatmap || []} />
        </Grid>
        <Grid item xs={12} md={6}>
          <MessageTypeDistributionChart data={data.messageDistribution || []} />
        </Grid>
        <Grid item xs={12} md={6}>
          <TopChannelsChart data={data.topChannels || []} />
        </Grid>
        <Grid item xs={12} md={6}>
          <InfluenceNetworkChart data={data.topUsers || []} />
        </Grid>
        {/* Add other charts as needed based on available data */}
      </Grid>
    </Box>
  );
}
