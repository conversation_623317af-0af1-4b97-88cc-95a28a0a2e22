#!/usr/bin/env python3
"""
Test script to debug the User Tracker and Global Search functionality.
"""

import requests
import json

def test_user_tracker():
    """Test the user tracker functionality"""
    base_url = "http://localhost:8025"
    
    print("Testing User Tracker")
    print("=" * 50)
    
    # Test with a valid user
    print("Testing with user 'balpreezy':")
    try:
        params = {
            'users': 'balpreezy',
            'from': '2025-07-01T00:00:00Z',
            'to': '2025-07-05T23:59:59Z'
        }
        response = requests.get(f"{base_url}/analytics", params=params)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

def test_global_search():
    """Test the global search functionality"""
    base_url = "http://localhost:8025"
    
    print("Testing Global Search")
    print("=" * 50)
    
    # Test with a valid user
    print("Testing with user 'balpreezy':")
    try:
        params = {
            'users': 'balpreezy',
            'from': '2025-07-01T00:00:00Z',
            'to': '2025-07-05T23:59:59Z'
        }
        response = requests.get(f"{base_url}/advanced-search", params=params)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

def main():
    print("Testing User and Search Functionality")
    print("=" * 60)
    
    test_user_tracker()
    test_global_search()
    
    print("\n" + "=" * 60)
    print("Tests Complete")

if __name__ == "__main__":
    main()
