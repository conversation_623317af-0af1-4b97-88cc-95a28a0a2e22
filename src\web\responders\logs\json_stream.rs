use crate::{    db::schema::StructuredMessage,    logs::{        schema::message::{BasicMessage, FullMessage, ResponseMessage},        stream::LogsStream,    },    Result,};use futures::{stream::TryChunks, Future, Stream, StreamExt, TryStreamExt};use std::{    pin::Pin,    task::{Context, Poll},};use tokio::pin;const HEADER: &str = r#"{"messages":["#;const FOOTER: &str = r#"]}"#;/// Rough estimation of how big a single message is in JSON format
const JSON_MESSAGE_SIZE: usize = 1024;
const CHUNK_SIZE: usize = 3000;pub enum JsonResponseType {    Basic,    Full,}pub struct JsonLogsStream {    inner: TryChunks<LogsStream>,    is_start: bool,    is_end: bool,    response_type: JsonResponseType,    first_message_written: bool,}impl JsonLogsStream {    pub fn new(stream: LogsStream, response_type: JsonResponseType) -> Self {        let inner = stream.try_chunks(CHUNK_SIZE);        Self {            inner,            is_start: true,            is_end: false,            response_type,            first_message_written: false,        }    }    fn serialize_chunk<'a, T: ResponseMessage<'a>>(        &mut self,        messages: &'a [StructuredMessage<'a>],    ) -> Vec<u8> {        let mut buf = Vec::with_capacity(JSON_MESSAGE_SIZE * messages.len());        if self.is_start {            buf.extend_from_slice(HEADER.as_bytes());            self.is_start = false;        }        for (i, msg) in messages.iter().enumerate() {            if let Some(parsed_message) = T::from_structured(msg).ok() {                if self.first_message_written || i > 0 {                    buf.push(b',');                }                serde_json::to_writer(&mut buf, &parsed_message).unwrap();                self.first_message_written = true;            }        }        buf    }}impl Stream for JsonLogsStream {
    type Item = Result<Vec<u8>>;

    fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        if self.is_end {
            return Poll::Ready(None);
        }

        let fut = self.inner.next();
        pin!(fut);

        match fut.poll(cx) {
            Poll::Ready(Some(result)) => match result {
                Ok(chunks) => {
                    let mut buf = Vec::new();
                    for chunk in chunks {
                        let chunk_buf = match self.response_type {
                            JsonResponseType::Basic => self.serialize_chunk::<BasicMessage>(&chunk),
                            JsonResponseType::Full => self.serialize_chunk::<FullMessage>(&chunk),
                        };
                        buf.extend_from_slice(&chunk_buf);
                    }

                    Poll::Ready(Some(Ok(buf)))
                }
                Err(err) => Poll::Ready(Some(Err(err.1))),
            },
            Poll::Ready(None) => {
                self.is_end = true;
                // If no messages were ever written, return an empty array
                if !self.first_message_written && self.is_start {
                    Poll::Ready(Some(Ok(b"{\"messages\":[]}".to_vec())))
                } else if self.first_message_written {
                    Poll::Ready(Some(Ok(FOOTER.as_bytes().to_vec())))
                } else {
                    Poll::Ready(None)
                }
            }
            Poll::Pending => Poll::Pending,
        }
    }
}
