{"name": "web", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.11", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^5.11.12", "@mui/system": "^7.2.0", "@mui/x-date-pickers": "^8.7.0", "@types/react-router-dom": "^5.3.3", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.7", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-linkify": "^1.0.0-alpha", "react-query": "^3.39.3", "react-router-dom": "^7.6.3", "react-window": "^1.8.8", "react-wordcloud": "^1.2.7", "runes": "^0.4.3", "swagger-ui-react": "^4.17.1", "typescript": "^4.9.5"}, "scripts": {"start": "vite", "build": "vite build"}, "devDependencies": {"@mui/types": "^7.2.3", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-linkify": "^1.0.1", "@types/react-window": "^1.8.5", "@types/runes": "^0.4.1", "@types/styled-components": "^5.1.26", "@types/swagger-ui-react": "^4.11.0", "@vitejs/plugin-react": "^3.1.0", "styled-components": "^5.3.8", "vite": "^4.1.4"}}